#!/usr/bin/env python3
"""
Migration script to add the manual_grading_adjustments table
"""

import sqlite3
import os
import sys

def run_migration():
    """Add the manual_grading_adjustments table to the database"""
    
    # Database path
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='manual_grading_adjustments'
        """)
        
        if cursor.fetchone():
            print("Table 'manual_grading_adjustments' already exists")
            return True
        
        # Create the manual_grading_adjustments table
        cursor.execute("""
            CREATE TABLE manual_grading_adjustments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                submission_id INTEGER NOT NULL,
                marking_point_id INTEGER NOT NULL,
                original_score REAL NOT NULL,
                adjusted_score REAL NOT NULL,
                teacher_id INTEGER NOT NULL,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                reason TEXT,
                FOREIGN KEY (submission_id) REFERENCES submissions (id),
                FOREIGN KEY (marking_point_id) REFERENCES marking_points (id),
                FOREIGN KEY (teacher_id) REFERENCES users (id),
                UNIQUE (submission_id, marking_point_id)
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("""
            CREATE INDEX idx_manual_adjustments_submission 
            ON manual_grading_adjustments (submission_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_manual_adjustments_teacher 
            ON manual_grading_adjustments (teacher_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_manual_adjustments_timestamp 
            ON manual_grading_adjustments (timestamp)
        """)
        
        conn.commit()
        print("Successfully created manual_grading_adjustments table and indexes")
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def rollback_migration():
    """Remove the manual_grading_adjustments table"""
    
    db_path = os.path.join('instance', 'database.db')
    
    if not os.path.exists(db_path):
        print(f"Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Drop indexes first
        cursor.execute("DROP INDEX IF EXISTS idx_manual_adjustments_submission")
        cursor.execute("DROP INDEX IF EXISTS idx_manual_adjustments_teacher")
        cursor.execute("DROP INDEX IF EXISTS idx_manual_adjustments_timestamp")
        
        # Drop the table
        cursor.execute("DROP TABLE IF EXISTS manual_grading_adjustments")
        
        conn.commit()
        print("Successfully removed manual_grading_adjustments table and indexes")
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
