#!/usr/bin/env python3
"""
Misconception Analyzer
Analyzes low-scoring questions from semantic analysis and identifies common student mistakes
using Gemini AI, processing multiple parts in parallel.
"""

import json
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import pandas as pd
from app import app
from models import db, Question, Part, Topic, Subject
import google.generativeai as genai
from dotenv import load_dotenv
import logging
from pilot_data_extractor import analyze_low_scoring_questions

# Load environment variables
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MISCONCEPTIONS_FILE = "question_misconceptions.json"

def load_existing_misconceptions():
    """Load existing misconceptions from JSON file"""
    if os.path.exists(MISCONCEPTIONS_FILE):
        try:
            with open(MISCONCEPTIONS_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading existing misconceptions: {e}")
    return {}

def save_misconceptions(misconceptions_data):
    """Save misconceptions to JSON file"""
    try:
        with open(MISCONCEPTIONS_FILE, 'w') as f:
            json.dump(misconceptions_data, f, indent=2)
        logger.info(f"Saved misconceptions to {MISCONCEPTIONS_FILE}")
    except Exception as e:
        logger.error(f"Error saving misconceptions: {e}")

def query_gemini_for_misconceptions(question_info, part_id, representative_answer, model_answer=None):
    """
    Query Gemini to identify common misconceptions for a low-scoring representative answer
    
    Args:
        question_info: Dictionary with question details
        part_id: The part ID
        representative_answer: The representative low-scoring student answer
        model_answer: The correct model answer (if available)
    
    Returns:
        List of common mistakes/misconceptions
    """
    try:
        # Get part details from database
        with app.app_context():
            part = Part.query.get(part_id)
            if not part:
                logger.warning(f"Part {part_id} not found in database")
                return []
            
            part_description = part.description
            part_model_answer = part.answer or model_answer or "Not provided"
        
        prompt = f"""
You are an expert educational analyst specializing in identifying student misconceptions and common mistakes.

QUESTION CONTEXT:
Subject: {question_info.get('subject_name', 'Unknown')}
Topic: {question_info.get('topic_name', 'Unknown')}
Question: {question_info.get('description', 'Unknown')}

PART DETAILS:
Part Description: {part_description}
Correct Answer: {part_model_answer}

STUDENT RESPONSE ANALYSIS:
This representative student answer scored poorly and represents a common pattern of responses:
"{representative_answer}"

TASK:
Analyze this low-scoring student response and identify the specific misconceptions, errors, or common mistakes that led to the poor performance. Focus on:

1. **Conceptual Misconceptions**: Fundamental misunderstandings of the underlying concepts
2. **Procedural Errors**: Mistakes in method, calculation, or approach
3. **Knowledge Gaps**: Missing information or incomplete understanding
4. **Common Confusions**: Typical mix-ups or misapplications students make

Provide a JSON array of specific, actionable misconceptions that teachers can address. Each misconception should be:
- Specific and clear
- Focused on the underlying error, not just "wrong answer"
- Useful for targeted remediation
- Written from an educational perspective

Format your response as a JSON array of strings, where each string describes one specific misconception or common mistake.

Example format:
["Misconception 1 description", "Misconception 2 description", ...]

Do not include any text outside the JSON array.
"""

        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(
            prompt,
            generation_config={
                'response_mime_type': 'application/json',
                'temperature': 0.3
            }
        )
        
        # Parse the JSON response
        misconceptions = json.loads(response.text)
        
        if isinstance(misconceptions, list):
            logger.info(f"Found {len(misconceptions)} misconceptions for part {part_id}")
            return misconceptions
        else:
            logger.warning(f"Unexpected response format for part {part_id}: {type(misconceptions)}")
            return []
            
    except Exception as e:
        logger.error(f"Error querying Gemini for part {part_id}: {e}")
        return []

def process_part_misconceptions(args):
    """
    Process misconceptions for a single part (for parallel processing)
    
    Args:
        args: Tuple of (question_id, question_info, part_id, representative_answer)
    
    Returns:
        Tuple of (question_id, part_id, misconceptions)
    """
    question_id, question_info, part_id, representative_answer = args
    
    logger.info(f"Processing misconceptions for Question {question_id}, Part {part_id}")
    
    misconceptions = query_gemini_for_misconceptions(
        question_info, 
        part_id, 
        representative_answer
    )
    
    return question_id, part_id, misconceptions

def analyze_misconceptions_parallel(low_scoring_analysis, max_workers=5):
    """
    Analyze misconceptions for all low-scoring questions in parallel
    
    Args:
        low_scoring_analysis: Dictionary from analyze_low_scoring_questions
        max_workers: Maximum number of parallel workers
    
    Returns:
        Dictionary with misconceptions organized by question_id and part_id
    """
    # Load existing misconceptions
    misconceptions_data = load_existing_misconceptions()
    
    # Prepare tasks for parallel processing
    tasks = []
    
    for question_id, analysis in low_scoring_analysis.items():
        question_info = analysis['question_info']
        
        for cluster in analysis['representative_clusters']:
            for part_id in cluster['part_ids']:
                # Skip if we already have misconceptions for this question/part
                if (str(question_id) in misconceptions_data and 
                    str(part_id) in misconceptions_data[str(question_id)]):
                    logger.info(f"Skipping Question {question_id}, Part {part_id} - already analyzed")
                    continue
                
                tasks.append((
                    question_id,
                    question_info,
                    part_id,
                    cluster['representative_answer']
                ))
    
    if not tasks:
        logger.info("No new misconceptions to analyze")
        return misconceptions_data
    
    logger.info(f"Processing {len(tasks)} part misconceptions with {max_workers} workers")
    
    # Process in parallel
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {executor.submit(process_part_misconceptions, task): task for task in tasks}
        
        for future in as_completed(future_to_task):
            try:
                question_id, part_id, misconceptions = future.result()
                
                # Store results
                question_key = str(question_id)
                part_key = str(part_id)
                
                if question_key not in misconceptions_data:
                    misconceptions_data[question_key] = {}
                
                misconceptions_data[question_key][part_key] = {
                    'common_mistakes': misconceptions
                }
                
                logger.info(f"Completed Question {question_id}, Part {part_id}: {len(misconceptions)} misconceptions")
                
            except Exception as e:
                task = future_to_task[future]
                logger.error(f"Error processing task {task}: {e}")
    
    # Save updated misconceptions
    save_misconceptions(misconceptions_data)
    
    return misconceptions_data

def load_cluster_analysis_from_excel(excel_file):
    """Load cluster analysis data from Excel file"""
    try:
        # Read the Question Semantic Analysis sheet
        df = pd.read_excel(excel_file, sheet_name='Question Semantic Analysis')

        # Convert to the expected format
        cluster_analysis = []
        for _, row in df.iterrows():
            # Handle NaN values in question description
            question_desc = row['Question Description'] if pd.notna(row['Question Description']) else f"Question {row['Question ID']}"

            cluster_analysis.append({
                'question_id': row['Question ID'],
                'question_description': question_desc,
                'topic_name': row['Topic'],
                'subject_name': row['Subject'],
                'cluster_id': row['Cluster ID'],
                'size': row['Cluster Size'],
                'representative_answer': row['Representative Answer'],
                'part_ids': [int(x.strip()) for x in str(row['Part IDs']).split(',') if x.strip().isdigit()],
                'avg_score': row['Average Score'],
                'max_score': row['Max Score'],
                'acceptance_rate': row['Acceptance Rate (%)'],
                'correct_submissions': row['Correct Submissions']
            })

        logger.info(f"Loaded {len(cluster_analysis)} clusters from Excel file")
        return cluster_analysis

    except Exception as e:
        logger.error(f"Error loading cluster analysis from Excel: {e}")
        return []

def import_misconceptions_from_pilot_data(excel_file, min_cluster_size=3, force_update=False):
    """
    Import misconceptions from pilot testing data based on low-scoring clusters

    Args:
        excel_file: Path to the pilot testing Excel file
        min_cluster_size: Minimum cluster size to consider (default: 3)
        force_update: Whether to add to existing misconceptions or replace them

    Returns:
        Dictionary with import results
    """
    try:
        from misconception_utils import load_misconceptions, save_misconceptions

        # Load cluster analysis data
        cluster_data = load_cluster_analysis_from_excel(excel_file)

        if not cluster_data:
            return {'success': False, 'error': 'No cluster data found in Excel file'}

        # Load existing misconceptions
        existing_misconceptions = load_misconceptions()

        # Group clusters by question and part
        question_part_clusters = defaultdict(list)
        for cluster in cluster_data:
            question_id = cluster['question_id']
            part_ids = cluster['part_ids']

            # Add cluster to each part it belongs to
            for part_id in part_ids:
                question_part_clusters[(question_id, part_id)].append(cluster)

        # Process each question-part combination
        import_stats = {
            'total_processed': 0,
            'misconceptions_added': 0,
            'questions_updated': set(),
            'parts_updated': set(),
            'skipped_small_clusters': 0,
            'skipped_high_scoring': 0
        }

        for (question_id, part_id), clusters in question_part_clusters.items():
            import_stats['total_processed'] += 1

            # Filter clusters by criteria
            valid_clusters = []
            for cluster in clusters:
                # Check cluster size
                if cluster['size'] < min_cluster_size:
                    import_stats['skipped_small_clusters'] += 1
                    continue

                # Check if it's a low-scoring cluster (avg_score < max_score)
                if cluster['avg_score'] >= cluster['max_score']:
                    import_stats['skipped_high_scoring'] += 1
                    continue

                valid_clusters.append(cluster)

            if not valid_clusters:
                continue

            # Sort clusters by frequency (size) and significance (score difference)
            valid_clusters.sort(key=lambda c: (
                c['size'],  # Prioritize larger clusters
                c['max_score'] - c['avg_score']  # Prioritize larger score gaps
            ), reverse=True)

            # Generate misconceptions from clusters
            new_misconceptions = []
            for cluster in valid_clusters:
                # Create misconception text based on representative answer
                representative_answer = cluster['representative_answer']
                score_gap = cluster['max_score'] - cluster['avg_score']
                acceptance_rate = cluster['acceptance_rate']

                # Generate misconception description
                misconception = f"Students often provide answers like: '{representative_answer}' " \
                              f"(avg score: {cluster['avg_score']:.1f}/{cluster['max_score']}, " \
                              f"acceptance rate: {acceptance_rate:.1f}%, " \
                              f"cluster size: {cluster['size']})"

                new_misconceptions.append(misconception)

            if new_misconceptions:
                # Update misconceptions data
                question_key = str(question_id)
                part_key = str(part_id)

                if question_key not in existing_misconceptions:
                    existing_misconceptions[question_key] = {}

                if part_key not in existing_misconceptions[question_key]:
                    existing_misconceptions[question_key][part_key] = {'common_mistakes': []}

                # Add or replace misconceptions
                if force_update:
                    existing_misconceptions[question_key][part_key]['common_mistakes'] = new_misconceptions
                else:
                    # Add to existing, avoiding duplicates
                    existing_list = existing_misconceptions[question_key][part_key]['common_mistakes']
                    for misconception in new_misconceptions:
                        if misconception not in existing_list:
                            existing_list.append(misconception)

                import_stats['misconceptions_added'] += len(new_misconceptions)
                import_stats['questions_updated'].add(question_id)
                import_stats['parts_updated'].add((question_id, part_id))

        # Save updated misconceptions
        if import_stats['misconceptions_added'] > 0:
            success = save_misconceptions(existing_misconceptions)
            if not success:
                return {'success': False, 'error': 'Failed to save misconceptions'}

        # Convert sets to counts for JSON serialization
        import_stats['questions_updated'] = len(import_stats['questions_updated'])
        import_stats['parts_updated'] = len(import_stats['parts_updated'])

        return {
            'success': True,
            'stats': import_stats,
            'message': f"Imported {import_stats['misconceptions_added']} misconceptions "
                      f"for {import_stats['parts_updated']} parts across "
                      f"{import_stats['questions_updated']} questions"
        }

    except Exception as e:
        logger.error(f"Error importing misconceptions from pilot data: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main function to run misconception analysis"""
    print("=== MISCONCEPTION ANALYSIS ===")

    # Look for existing Excel file
    excel_files = [f for f in os.listdir('.') if f.startswith('pilot_testing_report_') and f.endswith('.xlsx')]

    if not excel_files:
        print("No pilot testing report Excel file found.")
        print("Please run pilot_data_extractor.py first to generate the analysis data.")
        return

    # Use the most recent Excel file
    excel_file = sorted(excel_files)[-1]
    print(f"Loading cluster analysis from: {excel_file}")

    # Load cluster analysis data
    cluster_analysis = load_cluster_analysis_from_excel(excel_file)

    if not cluster_analysis:
        print("No cluster analysis data available. Please check the Excel file.")
        return

    # Analyze low-scoring questions
    low_scoring_analysis = analyze_low_scoring_questions(cluster_analysis, score_threshold=75.0)

    if not low_scoring_analysis:
        print("No low-scoring questions found.")
        return

    # Analyze misconceptions in parallel
    misconceptions_data = analyze_misconceptions_parallel(low_scoring_analysis, max_workers=3)

    print(f"\nMisconception analysis complete!")
    print(f"Results saved to {MISCONCEPTIONS_FILE}")
    print(f"Analyzed {len(misconceptions_data)} questions")

if __name__ == "__main__":
    main()
