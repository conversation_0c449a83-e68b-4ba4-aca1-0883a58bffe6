{% extends "base.html" %}

{% block head %}
<style>
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .filter-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
    }
    .adjustment-row:hover {
        background-color: #f7fafc;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Manual Grading Adjustments</h1>
        <p class="mt-2 text-gray-600">View and analyze all manual grading adjustments made by teachers</p>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="analytics-card rounded-lg p-6 text-center">
            <div class="text-3xl font-bold">{{ analytics.total_adjustments }}</div>
            <div class="text-sm opacity-90">Total Adjustments</div>
        </div>
        <div class="bg-green-500 rounded-lg p-6 text-center text-white">
            <div class="text-3xl font-bold">{{ analytics.score_increases }}</div>
            <div class="text-sm opacity-90">Score Increases</div>
        </div>
        <div class="bg-red-500 rounded-lg p-6 text-center text-white">
            <div class="text-3xl font-bold">{{ analytics.score_decreases }}</div>
            <div class="text-sm opacity-90">Score Decreases</div>
        </div>
        <div class="bg-gray-500 rounded-lg p-6 text-center text-white">
            <div class="text-3xl font-bold">{{ analytics.no_change }}</div>
            <div class="text-sm opacity-90">No Score Change</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold mb-4">Filters</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                <select name="subject_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">All Subjects</option>
                    {% for subject in subjects %}
                        <option value="{{ subject.id }}" {% if filters.subject_id == subject.id %}selected{% endif %}>
                            {{ subject.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Teacher</label>
                <select name="teacher_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">All Teachers</option>
                    {% for teacher in teachers %}
                        <option value="{{ teacher.id }}" {% if filters.teacher_id == teacher.id %}selected{% endif %}>
                            {{ teacher.username }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input type="date" name="date_from" value="{{ filters.date_from or '' }}" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input type="date" name="date_to" value="{{ filters.date_to or '' }}" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Teacher Statistics -->
    {% if analytics.teacher_stats %}
    <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h3 class="text-lg font-semibold mb-4">Top Teachers by Adjustments</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for teacher_stat in analytics.teacher_stats %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span class="font-medium">{{ teacher_stat.username }}</span>
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                    {{ teacher_stat.adjustment_count }} adjustments
                </span>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Adjustments Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold">Recent Adjustments</h3>
        </div>
        
        {% if adjustments.items %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marking Point</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Change</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for adjustment in adjustments.items %}
                    <tr class="adjustment-row">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ adjustment.timestamp.strftime('%Y-%m-%d %H:%M') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ adjustment.teacher.username }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ adjustment.submission.user.username }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate" title="{{ adjustment.submission.question.description }}">
                                {{ adjustment.submission.question.description }}
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate" title="{{ adjustment.marking_point.description }}">
                                {{ adjustment.marking_point.description }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-500">{{ "%.1f"|format(adjustment.original_score) }}</span>
                                <i class="fas fa-arrow-right text-gray-400"></i>
                                <span class="font-medium {% if adjustment.adjusted_score > adjustment.original_score %}text-green-600{% elif adjustment.adjusted_score < adjustment.original_score %}text-red-600{% else %}text-gray-600{% endif %}">
                                    {{ "%.1f"|format(adjustment.adjusted_score) }}
                                </span>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            {% if adjustment.reason %}
                                <div class="max-w-xs truncate" title="{{ adjustment.reason }}">
                                    {{ adjustment.reason }}
                                </div>
                            {% else %}
                                <span class="text-gray-400 italic">No reason provided</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <a href="{{ url_for('submission_details', submission_id=adjustment.submission_id) }}" 
                               class="text-blue-600 hover:text-blue-800">
                                View Submission
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if adjustments.pages > 1 %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ adjustments.per_page * (adjustments.page - 1) + 1 }} to 
                    {{ adjustments.per_page * (adjustments.page - 1) + adjustments.items|length }} of 
                    {{ adjustments.total }} results
                </div>
                <div class="flex space-x-2">
                    {% if adjustments.has_prev %}
                        <a href="{{ url_for('view_manual_adjustments', page=adjustments.prev_num, **filters) }}" 
                           class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Previous</a>
                    {% endif %}
                    {% if adjustments.has_next %}
                        <a href="{{ url_for('view_manual_adjustments', page=adjustments.next_num, **filters) }}" 
                           class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Next</a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="px-6 py-12 text-center">
            <div class="text-gray-500">
                <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                <p class="text-lg">No manual adjustments found</p>
                <p class="text-sm">Try adjusting your filters or check back later</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
