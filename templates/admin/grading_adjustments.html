{% extends "base.html" %}

{% block head %}
<style>
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .filter-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
    }
    .adjustment-row:hover {
        background-color: #f7fafc;
    }
    .tab-button {
        @apply px-6 py-4 text-base font-semibold rounded-xl transition-all duration-200 border-2 min-w-0 flex-1;
    }
    .tab-button.active {
        @apply bg-blue-600 text-white border-blue-600 shadow-lg transform scale-105;
    }
    .tab-button:not(.active) {
        @apply bg-white text-gray-700 border-gray-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 shadow-md;
    }
    .tab-stats {
        @apply text-xs opacity-90 mt-1 font-normal;
    }
    .misconception-item {
        @apply bg-gray-50 border border-gray-200 rounded-lg p-3 mb-2;
    }
    .editable-field {
        @apply w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }
    .action-button {
        @apply px-3 py-1 text-sm rounded-md transition-colors;
    }
    .btn-edit {
        @apply bg-blue-100 text-blue-700 hover:bg-blue-200;
    }
    .btn-delete {
        @apply bg-red-100 text-red-700 hover:bg-red-200;
    }
    .btn-save {
        @apply bg-green-100 text-green-700 hover:bg-green-200;
    }
    .btn-cancel {
        @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Grading Adjustments</h1>
        <p class="mt-2 text-gray-600">Manage manual adjustments, common misconceptions, and additional context for grading</p>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button class="tab-button {% if current_tab == 'manual_adjustments' %}active{% endif %}"
                    onclick="switchTab('manual_adjustments', event)">
                <div class="flex items-center justify-center">
                    <i class="fas fa-edit mr-2 text-lg"></i>
                    <div class="text-left">
                        <div>Manual Adjustments</div>
                        <div class="tab-stats" id="manual-adjustments-stats">
                            {{ analytics.total_adjustments }} total adjustments
                        </div>
                    </div>
                </div>
            </button>
            <button class="tab-button {% if current_tab == 'misconceptions' %}active{% endif %}"
                    onclick="switchTab('misconceptions', event)">
                <div class="flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle mr-2 text-lg"></i>
                    <div class="text-left">
                        <div>Common Misconceptions</div>
                        <div class="tab-stats" id="misconceptions-stats">
                            Loading statistics...
                        </div>
                    </div>
                </div>
            </button>
            <button class="tab-button {% if current_tab == 'additional_context' %}active{% endif %}"
                    onclick="switchTab('additional_context', event)">
                <div class="flex items-center justify-center">
                    <i class="fas fa-info-circle mr-2 text-lg"></i>
                    <div class="text-left">
                        <div>Additional Context</div>
                        <div class="tab-stats" id="context-stats">
                            Loading statistics...
                        </div>
                    </div>
                </div>
            </button>
        </div>
    </div>

    <!-- Manual Adjustments Tab -->
    <div id="manual_adjustments_tab" class="tab-content {% if current_tab != 'manual_adjustments' %}hidden{% endif %}">
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="analytics-card rounded-lg p-6 text-center">
                <div class="text-3xl font-bold">{{ analytics.total_adjustments }}</div>
                <div class="text-sm opacity-90">Total Adjustments</div>
            </div>
            <div class="bg-green-500 rounded-lg p-6 text-center text-white">
                <div class="text-3xl font-bold">{{ analytics.score_increases }}</div>
                <div class="text-sm opacity-90">Score Increases</div>
            </div>
            <div class="bg-red-500 rounded-lg p-6 text-center text-white">
                <div class="text-3xl font-bold">{{ analytics.score_decreases }}</div>
                <div class="text-sm opacity-90">Score Decreases</div>
            </div>
            <div class="bg-gray-500 rounded-lg p-6 text-center text-white">
                <div class="text-3xl font-bold">{{ analytics.no_change }}</div>
                <div class="text-sm opacity-90">No Score Change</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold mb-4">Filters</h3>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <input type="hidden" name="tab" value="manual_adjustments">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                    <select name="subject_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Subjects</option>
                        {% for subject in subjects %}
                            <option value="{{ subject.id }}" {% if filters.subject_id == subject.id %}selected{% endif %}>
                                {{ subject.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Teacher</label>
                    <select name="teacher_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Teachers</option>
                        {% for teacher in teachers %}
                            <option value="{{ teacher.id }}" {% if filters.teacher_id == teacher.id %}selected{% endif %}>
                                {{ teacher.username }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
                    <select name="question_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Questions</option>
                        {% for question in questions %}
                            <option value="{{ question.id }}" {% if filters.question_id == question.id %}selected{% endif %}>
                                {{ question.title or ("Q" + question.id|string) }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                    <input type="date" name="date_from" value="{{ filters.date_from or '' }}" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                    <input type="date" name="date_to" value="{{ filters.date_to or '' }}" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Teacher Statistics -->
        {% if analytics.teacher_stats %}
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h3 class="text-lg font-semibold mb-4">Top Teachers by Adjustments</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for teacher_stat in analytics.teacher_stats %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="font-medium">{{ teacher_stat.username }}</span>
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                        {{ teacher_stat.adjustment_count }} adjustments
                    </span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Adjustments Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold">Recent Adjustments</h3>
            </div>
            
            {% if adjustments.items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marking Point</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Change</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for adjustment in adjustments.items %}
                        <tr class="adjustment-row" id="adjustment-{{ adjustment.id }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ adjustment.timestamp.strftime('%Y-%m-%d %H:%M') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ adjustment.teacher.username }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ adjustment.submission.user.username }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="max-w-xs truncate" title="{{ adjustment.submission.question.description or 'No description' }}">
                                    {{ adjustment.submission.question.description or 'No description' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="max-w-xs truncate" title="{{ adjustment.marking_point.description }}">
                                    {{ adjustment.marking_point.description }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-500">{{ "%.1f"|format(adjustment.original_score) }}</span>
                                    <i class="fas fa-arrow-right text-gray-400"></i>
                                    <span class="font-medium {% if adjustment.adjusted_score > adjustment.original_score %}text-green-600{% elif adjustment.adjusted_score < adjustment.original_score %}text-red-600{% else %}text-gray-600{% endif %}">
                                        {{ "%.1f"|format(adjustment.adjusted_score) }}
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="adjustment-reason" data-adjustment-id="{{ adjustment.id }}">
                                    {% if adjustment.reason %}
                                        <div class="max-w-xs truncate reason-display" title="{{ adjustment.reason }}">
                                            {{ adjustment.reason }}
                                        </div>
                                    {% else %}
                                        <span class="text-gray-400 italic reason-display">No reason provided</span>
                                    {% endif %}
                                    <textarea class="editable-field reason-edit hidden" rows="2">{{ adjustment.reason or '' }}</textarea>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex space-x-2">
                                    <button class="action-button btn-edit edit-adjustment" 
                                            data-adjustment-id="{{ adjustment.id }}"
                                            data-max-score="{{ adjustment.marking_point.score }}"
                                            data-current-score="{{ adjustment.adjusted_score }}">
                                        Edit
                                    </button>
                                    <button class="action-button btn-delete delete-adjustment" 
                                            data-adjustment-id="{{ adjustment.id }}">
                                        Delete
                                    </button>
                                    <a href="{{ url_for('submission_details', submission_id=adjustment.submission_id) }}" 
                                       class="action-button bg-gray-100 text-gray-700 hover:bg-gray-200">
                                        View
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if adjustments.pages > 1 %}
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing {{ adjustments.per_page * (adjustments.page - 1) + 1 }} to 
                        {{ adjustments.per_page * (adjustments.page - 1) + adjustments.items|length }} of 
                        {{ adjustments.total }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if adjustments.has_prev %}
                            <a href="{{ url_for('view_grading_adjustments', page=adjustments.prev_num, tab='manual_adjustments', **filters) }}" 
                               class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Previous</a>
                        {% endif %}
                        {% if adjustments.has_next %}
                            <a href="{{ url_for('view_grading_adjustments', page=adjustments.next_num, tab='manual_adjustments', **filters) }}" 
                               class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Next</a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="px-6 py-12 text-center">
                <div class="text-gray-500">
                    <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                    <p class="text-lg">No manual adjustments found</p>
                    <p class="text-sm">Try adjusting your filters or check back later</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Misconceptions Tab -->
    <div id="misconceptions_tab" class="tab-content {% if current_tab != 'misconceptions' %}hidden{% endif %}">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4">Manage Common Misconceptions</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Select Question</label>
                        <select id="misconception-question-select" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">Select a question...</option>
                            <!-- Options will be populated by JavaScript with indicators -->
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Or Enter Question ID</label>
                        <div class="flex space-x-2">
                            <input type="number" id="misconception-question-id-input" placeholder="e.g. 123"
                                   class="flex-1 border border-gray-300 rounded-md px-3 py-2" min="1">
                            <button id="load-question-by-id" class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Select Part</label>
                        <select id="misconception-part-select" class="w-full border border-gray-300 rounded-md px-3 py-2" disabled>
                            <option value="">Select a question first...</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button id="load-all-misconceptions" class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm">
                            Load All Parts
                        </button>
                        <button id="refresh-misconceptions" class="bg-gray-500 text-white px-3 py-2 rounded-md hover:bg-gray-600 text-sm" disabled>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Analyzer Tools Section -->
                <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-3">
                        <i class="fas fa-robot mr-2"></i>Misconception Analyzer Tools
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <button id="analyze-selected-question" class="w-full bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 disabled:bg-gray-400" disabled>
                                <i class="fas fa-search mr-2"></i>Analyze Selected Question
                            </button>
                            <p class="text-xs text-yellow-700 mt-1">Generate misconceptions for all parts of the selected question</p>
                        </div>
                        <div>
                            <button id="analyze-all-empty" class="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700">
                                <i class="fas fa-magic mr-2"></i>Analyze All Empty Questions
                            </button>
                            <p class="text-xs text-orange-700 mt-1">Generate misconceptions for all questions without any</p>
                        </div>
                    </div>

                </div>

                <!-- Quick Info Display -->
                <div id="misconceptions-quick-info" class="hidden mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="font-medium text-blue-800" id="selected-question-info"></span>
                            <span class="text-blue-600 ml-2" id="selected-part-info"></span>
                        </div>
                        <div class="text-sm text-blue-600" id="misconceptions-count"></div>
                    </div>
                </div>
            </div>

            <div id="misconceptions-content" class="hidden">
                <div id="question-info" class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <!-- Question info will be loaded here -->
                </div>

                <div id="parts-misconceptions">
                    <!-- Parts and their misconceptions will be loaded here -->
                </div>
            </div>

            <!-- Single Part Display -->
            <div id="single-part-misconceptions" class="hidden">
                <!-- Single part misconceptions will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Additional Context Tab -->
    <div id="additional_context_tab" class="tab-content {% if current_tab != 'additional_context' %}hidden{% endif %}">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4">Manage Additional Context</h3>
                <div class="flex space-x-4 mb-4">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Select Question</label>
                        <select id="context-question-select" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">Select a question...</option>
                            {% for question in questions %}
                                <option value="{{ question.id }}">
                                    {{ question.title or ("Question " + question.id|string) }}{% if question.description %} - {{ question.description[:100] }}...{% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button id="load-context" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Load Context
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="context-content" class="hidden">
                <div id="context-question-info" class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <!-- Question info will be loaded here -->
                </div>
                
                <div id="question-context-section" class="mb-6">
                    <!-- Question context will be loaded here -->
                </div>
                
                <div id="parts-context">
                    <!-- Parts and their context will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load statistics and questions on page load
document.addEventListener('DOMContentLoaded', function() {
    loadGradingStatistics();
    loadQuestionsWithIndicators();
});

function loadGradingStatistics() {
    fetch('/api/admin/get_grading_statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTabStatistics(data.data);
            } else {
                console.error('Error loading statistics:', data.error);
                // Set fallback statistics
                document.getElementById('misconceptions-stats').textContent = 'Statistics unavailable';
                document.getElementById('context-stats').textContent = 'Statistics unavailable';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('misconceptions-stats').textContent = 'Statistics unavailable';
            document.getElementById('context-stats').textContent = 'Statistics unavailable';
        });
}

function updateTabStatistics(stats) {
    // Update misconceptions stats
    const misconceptionsStats = document.getElementById('misconceptions-stats');
    if (stats.misconceptions.total_misconceptions > 0) {
        misconceptionsStats.innerHTML = `
            ${stats.misconceptions.total_parts} parts • ${stats.misconceptions.total_misconceptions} misconceptions
        `;
    } else {
        misconceptionsStats.textContent = 'No misconceptions defined';
    }

    // Update context stats
    const contextStats = document.getElementById('context-stats');
    if (stats.additional_context.total_items > 0) {
        contextStats.innerHTML = `
            ${stats.additional_context.questions_with_context} questions • ${stats.additional_context.parts_with_context} parts
        `;
    } else {
        contextStats.textContent = 'No additional context defined';
    }

    // Update manual adjustments stats (in case it changed)
    const adjustmentsStats = document.getElementById('manual-adjustments-stats');
    if (stats.manual_adjustments.total_adjustments > 0) {
        adjustmentsStats.textContent = `${stats.manual_adjustments.total_adjustments} total adjustments`;
    } else {
        adjustmentsStats.textContent = 'No adjustments made';
    }
}

function loadQuestionsWithIndicators() {
    fetch('/api/admin/get_questions_with_misconceptions')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateQuestionDropdowns(data.questions);
            } else {
                console.error('Error loading questions:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function populateQuestionDropdowns(questions) {
    const misconceptionSelect = document.getElementById('misconception-question-select');
    const contextSelect = document.getElementById('context-question-select');

    // Clear existing options (except the first placeholder)
    misconceptionSelect.innerHTML = '<option value="">Select a question...</option>';
    contextSelect.innerHTML = '<option value="">Select a question...</option>';

    questions.forEach(question => {
        // Create option for misconceptions dropdown with indicator
        const misconceptionOption = document.createElement('option');
        misconceptionOption.value = question.id;
        misconceptionOption.dataset.questionTitle = question.title || `Question ${question.id}`;

        let indicator = '';
        if (question.has_misconceptions) {
            indicator = '✅ ';
        } else {
            indicator = '❌ ';
        }

        const title = question.title || `Question ${question.id}`;
        const description = question.description ? ` - ${question.description.substring(0, 80)}...` : '';
        const stats = question.has_misconceptions ?
            ` (${question.parts_with_misconceptions}/${question.total_parts} parts, ${question.total_misconceptions} misconceptions)` :
            ` (${question.total_parts} parts, no misconceptions)`;

        misconceptionOption.textContent = `${indicator}Q${question.id}: ${title}${description}${stats}`;
        misconceptionSelect.appendChild(misconceptionOption);

        // Create option for context dropdown (simpler, no misconception indicators)
        const contextOption = document.createElement('option');
        contextOption.value = question.id;
        contextOption.textContent = `Q${question.id}: ${title}${description}`;
        contextSelect.appendChild(contextOption);
    });

    // Store questions data for later use
    window.questionsData = questions;
}

// Tab switching functionality
function switchTab(tabName, event) {
    try {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.add('hidden');
        });

        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
            // Remove active styles
            btn.classList.remove('bg-blue-600', 'text-white', 'border-blue-600', 'shadow-lg', 'transform', 'scale-105');
            // Add inactive styles
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200', 'shadow-md');
        });

        // Show selected tab content
        const tabContent = document.getElementById(tabName + '_tab');
        if (tabContent) {
            tabContent.classList.remove('hidden');
        } else {
            console.error(`Tab content not found: ${tabName}_tab`);
        }

        // Find and activate the correct button
        let activeButton = null;

        // Try to get button from event first
        if (event && event.target) {
            activeButton = event.target.closest('.tab-button');
        }

        // If no event or button not found, find by tab name
        if (!activeButton) {
            const allButtons = document.querySelectorAll('.tab-button');
            allButtons.forEach(btn => {
                const onclick = btn.getAttribute('onclick');
                if (onclick && onclick.includes(`'${tabName}'`)) {
                    activeButton = btn;
                }
            });
        }

        // Apply active styles to the button
        if (activeButton) {
            activeButton.classList.add('active');
            // Remove inactive styles
            activeButton.classList.remove('bg-white', 'text-gray-700', 'border-gray-200', 'shadow-md');
            // Add active styles
            activeButton.classList.add('bg-blue-600', 'text-white', 'border-blue-600', 'shadow-lg', 'transform', 'scale-105');
        } else {
            console.warn(`Could not find button for tab: ${tabName}`);
        }

        // Update URL with tab parameter
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.replaceState({}, '', url);

    } catch (error) {
        console.error('Error in switchTab:', error);
    }
}

// Manual Adjustments CRUD functionality
document.addEventListener('DOMContentLoaded', function() {
    // Edit adjustment functionality
    document.querySelectorAll('.edit-adjustment').forEach(button => {
        button.addEventListener('click', function() {
            const adjustmentId = this.dataset.adjustmentId;
            const maxScore = parseFloat(this.dataset.maxScore);
            const currentScore = parseFloat(this.dataset.currentScore);
            const row = document.getElementById(`adjustment-${adjustmentId}`);

            // Show edit mode
            const reasonDiv = row.querySelector('.adjustment-reason');
            const reasonDisplay = reasonDiv.querySelector('.reason-display');
            const reasonEdit = reasonDiv.querySelector('.reason-edit');

            reasonDisplay.classList.add('hidden');
            reasonEdit.classList.remove('hidden');

            // Create score edit field
            const scoreCell = row.querySelector('td:nth-child(6)');
            const scoreDisplay = scoreCell.querySelector('div');
            const scoreInput = document.createElement('input');
            scoreInput.type = 'number';
            scoreInput.step = '0.5';
            scoreInput.min = '0';
            scoreInput.max = maxScore;
            scoreInput.value = currentScore;
            scoreInput.className = 'w-20 p-1 border border-gray-300 rounded';

            scoreDisplay.classList.add('hidden');
            scoreCell.appendChild(scoreInput);

            // Replace action buttons
            const actionsCell = row.querySelector('td:nth-child(8)');
            const originalActions = actionsCell.innerHTML;
            actionsCell.innerHTML = `
                <div class="flex space-x-2">
                    <button class="action-button btn-save save-adjustment" data-adjustment-id="${adjustmentId}">Save</button>
                    <button class="action-button btn-cancel cancel-edit">Cancel</button>
                </div>
            `;

            // Cancel edit functionality
            actionsCell.querySelector('.cancel-edit').addEventListener('click', function() {
                reasonDisplay.classList.remove('hidden');
                reasonEdit.classList.add('hidden');
                scoreDisplay.classList.remove('hidden');
                scoreInput.remove();
                actionsCell.innerHTML = originalActions;

                // Re-attach event listeners
                attachEditDeleteListeners(row);
            });

            // Save edit functionality
            actionsCell.querySelector('.save-adjustment').addEventListener('click', function() {
                const newScore = parseFloat(scoreInput.value);
                const newReason = reasonEdit.value.trim();

                if (newScore < 0 || newScore > maxScore) {
                    alert(`Score must be between 0 and ${maxScore}`);
                    return;
                }

                // Send update request
                fetch('/api/admin/update_manual_adjustment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        adjustment_id: adjustmentId,
                        adjusted_score: newScore,
                        reason: newReason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update display
                        reasonDisplay.textContent = newReason || 'No reason provided';
                        if (!newReason) {
                            reasonDisplay.classList.add('text-gray-400', 'italic');
                        } else {
                            reasonDisplay.classList.remove('text-gray-400', 'italic');
                        }

                        // Update score display
                        const scoreSpan = scoreDisplay.querySelector('span:last-child');
                        scoreSpan.textContent = newScore.toFixed(1);

                        // Update color based on score change
                        const originalScore = parseFloat(row.querySelector('span.text-gray-500').textContent);
                        scoreSpan.className = 'font-medium ' +
                            (newScore > originalScore ? 'text-green-600' :
                             newScore < originalScore ? 'text-red-600' : 'text-gray-600');

                        // Exit edit mode
                        reasonDisplay.classList.remove('hidden');
                        reasonEdit.classList.add('hidden');
                        scoreDisplay.classList.remove('hidden');
                        scoreInput.remove();
                        actionsCell.innerHTML = originalActions;

                        // Re-attach event listeners
                        attachEditDeleteListeners(row);

                        showNotification('Adjustment updated successfully', 'success');
                    } else {
                        alert('Error updating adjustment: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating adjustment');
                });
            });
        });
    });

    // Delete adjustment functionality
    document.querySelectorAll('.delete-adjustment').forEach(button => {
        button.addEventListener('click', function() {
            const adjustmentId = this.dataset.adjustmentId;

            if (confirm('Are you sure you want to delete this manual adjustment?')) {
                fetch('/api/admin/delete_manual_adjustment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        adjustment_id: adjustmentId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the row
                        document.getElementById(`adjustment-${adjustmentId}`).remove();
                        showNotification('Adjustment deleted successfully', 'success');
                    } else {
                        alert('Error deleting adjustment: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting adjustment');
                });
            }
        });
    });
});

function attachEditDeleteListeners(row) {
    const editBtn = row.querySelector('.edit-adjustment');
    const deleteBtn = row.querySelector('.delete-adjustment');

    if (editBtn) {
        editBtn.addEventListener('click', function() {
            // Re-implement edit functionality
            // This is a simplified version - you might want to extract the edit logic into a separate function
        });
    }

    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            // Re-implement delete functionality
            // This is a simplified version - you might want to extract the delete logic into a separate function
        });
    }
}

// Misconceptions functionality
document.getElementById('misconception-question-select').addEventListener('change', function() {
    const questionId = this.value;
    const partSelect = document.getElementById('misconception-part-select');
    const refreshBtn = document.getElementById('refresh-misconceptions');
    const analyzeBtn = document.getElementById('analyze-selected-question');

    // Sync with question ID input field
    document.getElementById('misconception-question-id-input').value = questionId;

    // Clear previous content
    document.getElementById('misconceptions-content').classList.add('hidden');
    document.getElementById('single-part-misconceptions').classList.add('hidden');
    document.getElementById('misconceptions-quick-info').classList.add('hidden');

    if (!questionId) {
        partSelect.innerHTML = '<option value="">Select a question first...</option>';
        partSelect.disabled = true;
        refreshBtn.disabled = true;
        analyzeBtn.disabled = true;
        return;
    }

    // Enable analyzer button
    analyzeBtn.disabled = false;

    // Load parts for the selected question
    fetch(`/api/admin/get_question_misconceptions?question_id=${questionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate part dropdown
                partSelect.innerHTML = '<option value="">All parts</option>';
                data.data.parts.forEach(part => {
                    const option = document.createElement('option');
                    option.value = part.part_id;
                    option.textContent = `Part ${part.part_id}: ${part.part_description.substring(0, 50)}...`;
                    option.dataset.misconceptionsCount = part.misconceptions.length;
                    partSelect.appendChild(option);
                });
                partSelect.disabled = false;
                refreshBtn.disabled = false;

                // Store question data for later use
                window.currentQuestionData = data.data;

                // Update quick info
                updateQuickInfo();
            } else {
                alert('Error loading question parts: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading question parts');
        });
});

document.getElementById('misconception-part-select').addEventListener('change', function() {
    updateQuickInfo();

    const partId = this.value;
    if (partId && window.currentQuestionData) {
        // Display single part
        displaySinglePartMisconceptions(partId);
    } else {
        // Hide single part display
        document.getElementById('single-part-misconceptions').classList.add('hidden');
    }
});

document.getElementById('load-all-misconceptions').addEventListener('click', function() {
    const questionId = document.getElementById('misconception-question-select').value;
    if (!questionId) {
        alert('Please select a question first');
        return;
    }

    if (window.currentQuestionData) {
        displayMisconceptions(window.currentQuestionData);
    }
});

document.getElementById('refresh-misconceptions').addEventListener('click', function() {
    const questionId = document.getElementById('misconception-question-select').value;
    if (questionId) {
        // Trigger change event to reload data
        document.getElementById('misconception-question-select').dispatchEvent(new Event('change'));
    }
});

// Analyzer functionality
document.getElementById('analyze-selected-question').addEventListener('click', function() {
    const questionId = document.getElementById('misconception-question-select').value;
    if (!questionId) {
        alert('Please select a question first');
        return;
    }

    const questionData = window.questionsData?.find(q => q.id == questionId);
    const hasExisting = questionData?.has_misconceptions;

    let message = `Run misconception analyzer for Q${questionId}?`;
    if (hasExisting) {
        message += `\n\nWarning: This question already has ${questionData.total_misconceptions} misconceptions. This will add to existing ones.`;
    }

    if (confirm(message)) {
        runAnalyzerForQuestion(questionId, hasExisting);
    }
});

document.getElementById('analyze-all-empty').addEventListener('click', function() {
    if (!window.questionsData) {
        alert('Questions data not loaded yet. Please wait and try again.');
        return;
    }

    const emptyQuestions = window.questionsData.filter(q => !q.has_misconceptions);

    if (emptyQuestions.length === 0) {
        alert('All questions already have misconceptions!');
        return;
    }

    const message = `Run misconception analyzer for ${emptyQuestions.length} questions without misconceptions?\n\nThis may take several minutes.`;

    if (confirm(message)) {
        runAnalyzerForAllEmpty(emptyQuestions);
    }
});

// Question ID input functionality
document.getElementById('load-question-by-id').addEventListener('click', function() {
    const questionId = document.getElementById('misconception-question-id-input').value.trim();
    if (!questionId) {
        alert('Please enter a question ID');
        return;
    }

    // Set the dropdown to this question ID and trigger change
    const dropdown = document.getElementById('misconception-question-select');
    dropdown.value = questionId;
    dropdown.dispatchEvent(new Event('change'));
});

// Allow Enter key in question ID input
document.getElementById('misconception-question-id-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('load-question-by-id').click();
    }
});



function updateQuickInfo() {
    const questionSelect = document.getElementById('misconception-question-select');
    const partSelect = document.getElementById('misconception-part-select');
    const quickInfo = document.getElementById('misconceptions-quick-info');
    const questionInfo = document.getElementById('selected-question-info');
    const partInfo = document.getElementById('selected-part-info');
    const countInfo = document.getElementById('misconceptions-count');

    if (!questionSelect.value) {
        quickInfo.classList.add('hidden');
        return;
    }

    const selectedQuestionOption = questionSelect.options[questionSelect.selectedIndex];
    const questionTitle = selectedQuestionOption.dataset.questionTitle || `Question ${questionSelect.value}`;

    questionInfo.textContent = `Q${questionSelect.value}: ${questionTitle}`;

    if (partSelect.value) {
        const selectedPartOption = partSelect.options[partSelect.selectedIndex];
        const misconceptionsCount = selectedPartOption.dataset.misconceptionsCount || 0;
        partInfo.textContent = `| ${selectedPartOption.textContent}`;
        countInfo.textContent = `${misconceptionsCount} misconceptions`;
    } else {
        partInfo.textContent = '| All parts';
        if (window.currentQuestionData) {
            const totalMisconceptions = window.currentQuestionData.parts.reduce((sum, part) => sum + part.misconceptions.length, 0);
            countInfo.textContent = `${totalMisconceptions} total misconceptions`;
        }
    }

    quickInfo.classList.remove('hidden');
}

function displaySinglePartMisconceptions(partId) {
    if (!window.currentQuestionData) return;

    const part = window.currentQuestionData.parts.find(p => p.part_id == partId);
    if (!part) return;

    const singlePartDiv = document.getElementById('single-part-misconceptions');
    const questionData = window.currentQuestionData;

    singlePartDiv.innerHTML = `
        <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div class="mb-4">
                <h4 class="font-semibold text-lg text-gray-800">Q${questionData.question_id} - Part ${part.part_id}</h4>
                <p class="text-gray-600 mt-1">${part.part_description}</p>
                <div class="mt-2 text-sm text-gray-500">
                    Question: ${questionData.question_description || 'No description'}
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="font-medium">Common Misconceptions</h5>
                    <span class="text-sm text-gray-500">${part.misconceptions.length} misconceptions</span>
                </div>

                <div class="misconceptions-list" data-part-id="${part.part_id}">
                    ${part.misconceptions.map((misconception, index) => `
                        <div class="misconception-item mb-3" data-index="${index}">
                            <div class="flex items-start space-x-2">
                                <div class="flex-1">
                                    <textarea class="w-full editable-field misconception-text" rows="2" placeholder="Enter misconception...">${misconception}</textarea>
                                </div>
                                <button class="action-button btn-delete remove-misconception" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}

                    ${part.misconceptions.length === 0 ? `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-lightbulb text-3xl mb-2"></i>
                            <p>No misconceptions defined for this part yet.</p>
                            <p class="text-sm">Click "Add Misconception" to get started.</p>
                        </div>
                    ` : ''}
                </div>

                <div class="mt-4 flex space-x-2">
                    <button class="action-button bg-green-100 text-green-700 hover:bg-green-200 add-misconception" data-part-id="${part.part_id}">
                        <i class="fas fa-plus mr-1"></i> Add Misconception
                    </button>
                    <button class="action-button bg-blue-100 text-blue-700 hover:bg-blue-200 save-misconceptions" data-part-id="${part.part_id}" data-question-id="${questionData.question_id}">
                        <i class="fas fa-save mr-1"></i> Save Changes
                    </button>
                    <button class="action-button bg-yellow-100 text-yellow-700 hover:bg-yellow-200" onclick="importFromAnalyzer(${questionData.question_id}, ${part.part_id})">
                        <i class="fas fa-download mr-1"></i> Import from Analyzer
                    </button>
                </div>
            </div>
        </div>
    `;

    // Hide other content and show single part
    document.getElementById('misconceptions-content').classList.add('hidden');
    singlePartDiv.classList.remove('hidden');

    // Attach event listeners
    attachMisconceptionListeners();
}

function displayMisconceptions(questionData) {
    const contentDiv = document.getElementById('misconceptions-content');
    const questionInfoDiv = document.getElementById('question-info');
    const partsDiv = document.getElementById('parts-misconceptions');

    // Show question info
    questionInfoDiv.innerHTML = `
        <h4 class="font-semibold text-lg">Q${questionData.question_id}: ${questionData.question_title}</h4>
        <p class="text-gray-600 mt-2">${questionData.question_description || 'No description'}</p>
    `;

    // Show parts and misconceptions
    partsDiv.innerHTML = '';
    questionData.parts.forEach(part => {
        const partDiv = document.createElement('div');
        partDiv.className = 'mb-8 p-4 border border-gray-200 rounded-lg';
        partDiv.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h5 class="font-semibold">Part ${part.part_id}: ${part.part_description}</h5>
                <span class="text-sm text-gray-500">${part.misconceptions.length} misconceptions</span>
            </div>
            <div class="misconceptions-list" data-part-id="${part.part_id}">
                ${part.misconceptions.map((misconception, index) => `
                    <div class="misconception-item" data-index="${index}">
                        <div class="flex items-start space-x-2">
                            <textarea class="flex-1 editable-field misconception-text" rows="2">${misconception}</textarea>
                            <button class="action-button btn-delete remove-misconception" data-index="${index}">Remove</button>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="mt-4 flex space-x-2">
                <button class="action-button bg-green-100 text-green-700 hover:bg-green-200 add-misconception" data-part-id="${part.part_id}">
                    Add Misconception
                </button>
                <button class="action-button bg-blue-100 text-blue-700 hover:bg-blue-200 save-misconceptions" data-part-id="${part.part_id}" data-question-id="${questionData.question_id}">
                    Save Changes
                </button>
            </div>
        `;
        partsDiv.appendChild(partDiv);
    });

    // Attach event listeners
    attachMisconceptionListeners();

    // Hide single part and show all parts
    document.getElementById('single-part-misconceptions').classList.add('hidden');
    contentDiv.classList.remove('hidden');
}

function ensureMisconceptionsSection(partId, container) {
    // Ensure that a misconceptions section exists for the given part
    let misconceptionsSection = container.querySelector('.misconceptions-list');

    if (!misconceptionsSection) {
        // Create the misconceptions section
        const sectionHTML = `
            <div class="mt-4">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="font-medium">Common Misconceptions</h5>
                    <span class="text-sm text-gray-500">0 misconceptions</span>
                </div>
                <div class="misconceptions-list" data-part-id="${partId}">
                    <!-- Misconceptions will be added here -->
                </div>
            </div>
        `;

        // Find where to insert it (before the buttons)
        const buttonsContainer = container.querySelector('.mt-4.flex.space-x-2');
        if (buttonsContainer) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = sectionHTML;
            container.insertBefore(tempDiv.firstElementChild, buttonsContainer);
            misconceptionsSection = container.querySelector('.misconceptions-list');
        }
    }

    return misconceptionsSection;
}

function attachMisconceptionListeners() {
    // Add misconception buttons
    document.querySelectorAll('.add-misconception').forEach(button => {
        button.addEventListener('click', function() {
            const partId = this.getAttribute('data-part-id') || this.dataset.partId;

            if (!partId) {
                console.error('No partId found on add-misconception button');
                showNotification('Error: No part ID found', 'error');
                return;
            }

            // Try multiple selectors to find the misconceptions list
            let listDiv = document.querySelector(`[data-part-id="${partId}"] .misconceptions-list`);

            // If not found, try finding it relative to the button
            if (!listDiv) {
                // Look for the closest container and then find the list within it
                const container = this.closest('[data-part-id]') || this.closest('.p-4');
                if (container) {
                    listDiv = container.querySelector('.misconceptions-list');
                }
            }

            // If still not found, try a more general approach
            if (!listDiv) {
                // Find all misconceptions lists and match by part ID
                const allLists = document.querySelectorAll('.misconceptions-list');
                for (const list of allLists) {
                    if (list.dataset.partId === partId) {
                        listDiv = list;
                        break;
                    }
                }
            }

            if (!listDiv) {
                // Try to create a misconceptions section if it doesn't exist
                console.warn(`No misconceptions list found for part ${partId}, attempting to create one`);

                const partContainer = this.closest('[data-part-id]') || this.closest('.p-4');
                if (partContainer) {
                    listDiv = ensureMisconceptionsSection(partId, partContainer);
                }

                if (!listDiv) {
                    console.error(`Could not find or create misconceptions list for part ${partId}`);
                    showNotification('Error: Could not find misconceptions list', 'error');
                    return;
                }
            }

            const newIndex = listDiv.children.length;

            const newMisconceptionDiv = document.createElement('div');
            newMisconceptionDiv.className = 'misconception-item';
            newMisconceptionDiv.dataset.index = newIndex;
            newMisconceptionDiv.innerHTML = `
                <div class="flex items-start space-x-2">
                    <div class="flex-1">
                        <textarea class="w-full editable-field misconception-text" rows="2" placeholder="Enter new misconception..."></textarea>
                    </div>
                    <button class="action-button btn-delete remove-misconception" data-index="${newIndex}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            listDiv.appendChild(newMisconceptionDiv);

            // Attach remove listener to new button
            newMisconceptionDiv.querySelector('.remove-misconception').addEventListener('click', function() {
                newMisconceptionDiv.remove();
            });

            // Focus on the new textarea
            const newTextarea = newMisconceptionDiv.querySelector('textarea');
            if (newTextarea) {
                newTextarea.focus();
            }
        });
    });

    // Remove misconception buttons
    document.querySelectorAll('.remove-misconception').forEach(button => {
        button.addEventListener('click', function() {
            this.closest('.misconception-item').remove();
        });
    });

    // Save misconceptions buttons
    document.querySelectorAll('.save-misconceptions').forEach(button => {
        button.addEventListener('click', function() {
            const partId = this.getAttribute('data-part-id') || this.dataset.partId;
            const questionId = this.getAttribute('data-question-id') || this.dataset.questionId;

            if (!partId || !questionId) {
                console.error('Missing partId or questionId on save button');
                showNotification('Error: Missing part or question ID', 'error');
                return;
            }

            let listDiv = document.querySelector(`[data-part-id="${partId}"] .misconceptions-list`);

            // If not found, try alternative selectors
            if (!listDiv) {
                const container = this.closest('[data-part-id]') || this.closest('.p-4');
                if (container) {
                    listDiv = container.querySelector('.misconceptions-list');
                }
            }

            // If still not found, try to find it in the currently displayed question parts
            if (!listDiv) {
                const allLists = document.querySelectorAll('.misconceptions-list');
                for (const list of allLists) {
                    if (list.dataset.partId === partId || list.getAttribute('data-part-id') === partId) {
                        listDiv = list;
                        break;
                    }
                }
            }

            const misconceptions = [];

            if (listDiv) {
                // Get misconceptions from existing list
                listDiv.querySelectorAll('.misconception-text').forEach(textarea => {
                    const text = textarea.value.trim();
                    if (text) {
                        misconceptions.push(text);
                    }
                });
            } else {
                // If no list found, this might be a new part without misconceptions yet
                // Check if we're trying to save from a context where misconceptions were just added
                console.warn(`No misconceptions list found for part ${partId}, treating as empty list`);

                // Try to find any misconception textareas in the current context
                const contextContainer = this.closest('.p-4') || this.closest('.bg-white');
                if (contextContainer) {
                    const textareas = contextContainer.querySelectorAll('.misconception-text');
                    textareas.forEach(textarea => {
                        const text = textarea.value.trim();
                        if (text) {
                            misconceptions.push(text);
                        }
                    });
                }
            }

            fetch('/api/admin/update_part_misconceptions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: questionId,
                    part_id: partId,
                    misconceptions: misconceptions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');

                    // Update the stored data
                    if (window.currentQuestionData) {
                        const part = window.currentQuestionData.parts.find(p => p.part_id == partId);
                        if (part) {
                            part.misconceptions = misconceptions;
                        }
                    }

                    // Update the misconceptions count display
                    const container = this.closest('[data-part-id]') || this.closest('.p-4');
                    if (container) {
                        const countSpan = container.querySelector('.text-sm.text-gray-500');
                        if (countSpan) {
                            const count = misconceptions.length;
                            countSpan.textContent = `${count} misconception${count !== 1 ? 's' : ''}`;
                        }
                    }

                    // Update quick info
                    updateQuickInfo();

                    // Refresh statistics
                    loadGradingStatistics();

                    // Refresh the questions list to update indicators
                    loadQuestionsWithIndicators();
                } else {
                    alert('Error saving misconceptions: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving misconceptions');
            });
        });
    });
}

// Import from analyzer functionality (for single parts)
function importFromAnalyzer(questionId, partId) {
    if (confirm('This will run the misconception analyzer for this specific part. Continue?')) {
        runAnalyzerForPart(questionId, partId);
    }
}

function runAnalyzerForQuestion(questionId, hasExisting = false) {
    const button = document.getElementById('analyze-selected-question');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Analyzing...';

    showNotification('Running misconception analyzer...', 'info');

    fetch('/api/admin/run_misconception_analyzer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            question_id: questionId,
            force_update: hasExisting
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Refresh the questions data and current view
            loadQuestionsWithIndicators();
            loadGradingStatistics();

            // If we have a question selected, refresh its data
            if (window.currentQuestionData && window.currentQuestionData.question_id == questionId) {
                setTimeout(() => {
                    document.getElementById('misconception-question-select').dispatchEvent(new Event('change'));
                }, 500);
            }
        } else {
            showNotification('Analyzer error: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error running analyzer', 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function runAnalyzerForPart(questionId, partId) {
    showNotification('Running analyzer for specific part...', 'info');

    fetch('/api/admin/run_misconception_analyzer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            question_id: questionId,
            part_id: partId,
            force_update: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Update the current part data
            if (window.currentQuestionData) {
                const part = window.currentQuestionData.parts.find(p => p.part_id == partId);
                if (part && data.misconceptions) {
                    part.misconceptions = data.misconceptions;
                }
            }

            // Refresh displays
            loadQuestionsWithIndicators();
            loadGradingStatistics();
            updateQuickInfo();

            // Refresh the current part display
            const currentPartId = document.getElementById('misconception-part-select').value;
            if (currentPartId == partId) {
                displaySinglePartMisconceptions(partId);
            }
        } else {
            showNotification('Analyzer error: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error running analyzer for part', 'error');
    });
}

function runAnalyzerForAllEmpty(emptyQuestions) {
    const button = document.getElementById('analyze-all-empty');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Analyzing...';

    showNotification(`Starting analysis of ${emptyQuestions.length} questions...`, 'info');

    let completed = 0;
    let successful = 0;

    // Process questions sequentially to avoid overwhelming the server
    async function processNext(index) {
        if (index >= emptyQuestions.length) {
            // All done
            button.disabled = false;
            button.innerHTML = originalText;

            showNotification(`Analysis complete! Successfully processed ${successful}/${emptyQuestions.length} questions.`, 'success');

            // Refresh all data
            loadQuestionsWithIndicators();
            loadGradingStatistics();
            return;
        }

        const question = emptyQuestions[index];

        try {
            const response = await fetch('/api/admin/run_misconception_analyzer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: question.id,
                    force_update: false
                })
            });

            const data = await response.json();
            completed++;

            if (data.success) {
                successful++;
                showNotification(`Q${question.id}: ${data.message}`, 'success');
            } else {
                showNotification(`Q${question.id}: ${data.error}`, 'warning');
            }

            // Update progress
            button.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Analyzing... (${completed}/${emptyQuestions.length})`;

            // Wait a bit before processing next to avoid overwhelming the server
            setTimeout(() => processNext(index + 1), 1000);

        } catch (error) {
            completed++;
            console.error(`Error processing Q${question.id}:`, error);
            showNotification(`Q${question.id}: Error`, 'error');

            // Continue with next question
            setTimeout(() => processNext(index + 1), 1000);
        }
    }

    // Start processing
    processNext(0);
}



// Enhanced notification function with better styling
function showNotification(message, type = 'info') {
    // Remove existing notifications
    document.querySelectorAll('.notification').forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = `notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
                type === 'success' ? 'check-circle' :
                type === 'error' ? 'exclamation-circle' :
                type === 'warning' ? 'exclamation-triangle' :
                'info-circle'
            } mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);

    // Add click to dismiss
    notification.addEventListener('click', () => {
        notification.remove();
    });
}

// Additional Context functionality
document.getElementById('load-context').addEventListener('click', function() {
    const questionId = document.getElementById('context-question-select').value;
    if (!questionId) {
        alert('Please select a question first');
        return;
    }

    fetch(`/api/admin/get_question_context?question_id=${questionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayContext(data.data);
            } else {
                alert('Error loading context: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading context');
        });
});

function displayContext(questionData) {
    const contentDiv = document.getElementById('context-content');
    const questionInfoDiv = document.getElementById('context-question-info');
    const questionContextDiv = document.getElementById('question-context-section');
    const partsDiv = document.getElementById('parts-context');

    // Show question info
    questionInfoDiv.innerHTML = `
        <h4 class="font-semibold text-lg">${questionData.question_title}</h4>
        <p class="text-gray-600 mt-2">${questionData.question_description}</p>
    `;

    // Show question context
    questionContextDiv.innerHTML = `
        <div class="p-4 border border-gray-200 rounded-lg">
            <h5 class="font-semibold mb-3">Question Additional Context</h5>
            <textarea id="question-context-text" class="editable-field" rows="4" placeholder="Enter additional context for the entire question...">${questionData.question_comments}</textarea>
            <div class="mt-3">
                <button class="action-button bg-blue-100 text-blue-700 hover:bg-blue-200 save-question-context" data-question-id="${questionData.question_id}">
                    Save Question Context
                </button>
            </div>
        </div>
    `;

    // Show parts context
    partsDiv.innerHTML = '';
    questionData.parts.forEach(part => {
        const partDiv = document.createElement('div');
        partDiv.className = 'mb-6 p-4 border border-gray-200 rounded-lg';
        partDiv.innerHTML = `
            <h5 class="font-semibold mb-3">Part ${part.part_id}: ${part.part_description}</h5>
            <textarea class="editable-field part-context-text" rows="3" placeholder="Enter additional context for this part..." data-part-id="${part.part_id}">${part.part_comments}</textarea>
            <div class="mt-3">
                <button class="action-button bg-blue-100 text-blue-700 hover:bg-blue-200 save-part-context" data-part-id="${part.part_id}" data-question-id="${questionData.question_id}">
                    Save Part Context
                </button>
            </div>
        `;
        partsDiv.appendChild(partDiv);
    });

    // Attach event listeners
    attachContextListeners(questionData.question_id);

    contentDiv.classList.remove('hidden');
}

function attachContextListeners(questionId) {
    // Save question context
    document.querySelector('.save-question-context').addEventListener('click', function() {
        const comments = document.getElementById('question-context-text').value.trim();

        fetch('/api/admin/update_question_context', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question_id: questionId,
                comments: comments
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                // Refresh statistics
                loadGradingStatistics();
            } else {
                alert('Error saving context: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving context');
        });
    });

    // Save part context
    document.querySelectorAll('.save-part-context').forEach(button => {
        button.addEventListener('click', function() {
            const partId = this.dataset.partId;
            const textarea = document.querySelector(`[data-part-id="${partId}"]`);
            const comments = textarea.value.trim();

            fetch('/api/admin/update_question_context', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: questionId,
                    part_id: partId,
                    comments: comments
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    // Refresh statistics
                    loadGradingStatistics();
                } else {
                    alert('Error saving context: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving context');
            });
        });
    });
}


</script>
{% endblock %}
